import bettercam
import torch
import time
import math
import keyboard
import threading
import serial
import tkinter as tk
import pywintypes
import win32api
import win32con
from ultralytics import YOLO
import queue
from concurrent.futures import ThreadPoolExecutor



class DeltaCooldown:
    """Delta-based cooldown system for better performance"""
    def __init__(self, cooldown_time):
        self.cooldown_time = cooldown_time
        self.last_trigger = 0

    def can_trigger(self):
        current_time = time.perf_counter()
        if current_time - self.last_trigger >= self.cooldown_time:
            self.last_trigger = current_time
            return True
        return False

    def reset(self):
        self.last_trigger = 0


class AsyncDetectionEngine:
    """Asynchronous YOLOv8 detection engine with frame buffering"""
    def __init__(self, model_path, max_queue_size=2):
        self.model = YOLO(model_path, task='detect')
        self.detection_queue = queue.Queue(maxsize=max_queue_size)
        self.result_queue = queue.Queue(maxsize=max_queue_size)
        self.executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix="YOLO")
        self.running = True
        self.latest_detections = []
        self.detection_thread = threading.Thread(target=self._detection_worker, daemon=True)
        self.detection_thread.start()

    def _detection_worker(self):
        """Worker thread for YOLOv8 inference"""
        while self.running:
            try:
                screenshot = self.detection_queue.get(timeout=0.1)
                if screenshot is None:
                    continue

                # Run inference
                results = self.model.predict(
                    screenshot,
                    imgsz=736,
                    classes=1,
                    conf=0.4,
                    max_det=300,
                    device="cuda:0",
                    verbose=False
                )

                # Process results efficiently without pandas
                detections = []
                if results and len(results) > 0 and results[0].boxes is not None:
                    boxes = results[0].boxes.xyxy.cpu().numpy()
                    confidences = results[0].boxes.conf.cpu().numpy() if results[0].boxes.conf is not None else None

                    for i, box in enumerate(boxes):
                        detection = {
                            'xmin': float(box[0]),
                            'ymin': float(box[1]),
                            'xmax': float(box[2]),
                            'ymax': float(box[3]),
                            'confidence': float(confidences[i]) if confidences is not None else 1.0
                        }
                        detections.append(detection)

                # Update latest detections (thread-safe)
                self.latest_detections = detections

            except queue.Empty:
                continue
            except Exception as e:
                print(f"Detection error: {e}")
                continue

    def submit_frame(self, screenshot):
        """Submit frame for detection (non-blocking)"""
        try:
            # Clear old frames if queue is full
            if self.detection_queue.full():
                try:
                    self.detection_queue.get_nowait()
                except queue.Empty:
                    pass

            self.detection_queue.put_nowait(screenshot)
        except queue.Full:
            pass  # Skip frame if queue is full

    def get_latest_detections(self):
        """Get latest detection results"""
        return self.latest_detections.copy()

    def stop(self):
        """Stop the detection engine"""
        self.running = False
        self.executor.shutdown(wait=False)


def labels():
    #This function contains all the labels used and is threaded so tikinter can run a ui
    global fps_label
    global assist_label
    global silent_label
    global aim_lock_label
    global fov_label
    fps_label = tk.Label(text = "  ", font=('Tahoma','10'), fg='white', bg='black')
    fps_label.master.overrideredirect(True)
    fps_label.master.geometry("+14+16")
    fps_label.master.lift()
    fps_label.master.wm_attributes("-topmost", True)
    fps_label.master.wm_attributes("-disabled", True)
    fps_label.master.wm_attributes("-transparentcolor", "black")
    fps_label.pack()
    fov_label = tk.Label(text = f"FOV: {activation_range}", font=('Tahoma','10'), fg='white', bg='black')
    fov_label.master.overrideredirect(True)
    fov_label.master.lift()
    fov_label.master.wm_attributes("-topmost", True)
    fov_label.master.wm_attributes("-disabled", True)
    fov_label.master.wm_attributes("-transparentcolor", "black")
    fov_label.pack()

    assist_label = tk.Label(text = "Aim Assist: Unactive", font=('Tahoma','10'), fg='red', bg='black')
    assist_label.master.overrideredirect(True)
    assist_label.master.lift()
    assist_label.master.wm_attributes("-topmost", True)
    assist_label.master.wm_attributes("-disabled", True)
    assist_label.master.wm_attributes("-transparentcolor", "black")
    assist_label.pack()


    silent_label = tk.Label(text = "Silent aim: Unactive", font=('Tahoma','10'), fg='red', bg='black')
    silent_label.master.overrideredirect(True)
    silent_label.master.lift()
    silent_label.master.wm_attributes("-topmost", True)
    silent_label.master.wm_attributes("-disabled", True)
    silent_label.master.wm_attributes("-transparentcolor", "black")
    silent_label.pack()

    aim_lock_label = tk.Label(text = "Aim Lock: Unactive", font=('Tahoma','10'), fg='red', bg='black')
    aim_lock_label.master.overrideredirect(True)
    aim_lock_label.master.lift()
    aim_lock_label.master.wm_attributes("-topmost", True)
    aim_lock_label.master.wm_attributes("-disabled", True)
    aim_lock_label.master.wm_attributes("-transparentcolor", "black")
    aim_lock_label.pack()


    hWindow = pywintypes.HANDLE(int(fps_label.master.frame(), 16))
    exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
    win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
    hWindow = pywintypes.HANDLE(int(assist_label.master.frame(), 16))
    exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
    win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
    hWindow = pywintypes.HANDLE(int(aim_lock_label.master.frame(), 16))
    exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
    win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
    fps_label.mainloop()



# Performance optimized configuration
SENS = 0.30
# Separate AIM_SPEED configurations for aim assist and silent aim
AIM_SPEED_ASSIST = 0.6  # Lower value for aim assist (prevents overshooting)
AIM_SPEED_SILENT = 1*(1/SENS)  # Original calculation from main.py for silent aim (3.33)
target_multiply = [0,1.01,1.025,1.05,1.05,1.05,1.05,1.05,1.05,1.05,1.05]
serialcomm = serial.Serial("COM9",115200,timeout = 0)
activation_range = 300

# Separate max distance limits for aim assist and silent aim
AIM_ASSIST_MAX_DISTANCE = 65  # Original aim assist distance limit
SILENT_AIM_MAX_DISTANCE = 300  # Much larger distance for silent aim (no practical limit)

# Debug: Let's add some debugging to understand the coordinate system
print(f"AIM_SPEED_ASSIST: {AIM_SPEED_ASSIST}")
print(f"AIM_SPEED_SILENT: {AIM_SPEED_SILENT}")
print(f"MONITOR_SCALE: 5, target_multiply[5]: {target_multiply[5]}")
print(f"Aim Assist Final multiplier: {AIM_SPEED_ASSIST * target_multiply[5]}")
print(f"Silent Aim Final multiplier: {AIM_SPEED_SILENT * target_multiply[5]}")
print("Press F7 to toggle aim lock (on/off)")
print("Hold 0x05 mouse button to run aim lock temporarily")
print("Press F9 to toggle aim assist")
print("Press F8 to toggle silent aim")

# Dynamic aim distance adjustment
class DynamicAimConfig:
    def __init__(self):
        self.base_distance = 65
        self.max_distance = 100
        self.min_distance = 40
        self.fps_threshold_high = 90
        self.fps_threshold_low = 60

    def get_aim_distance(self, current_fps):
        """Adjust aim distance based on current FPS"""
        if current_fps >= self.fps_threshold_high:
            return self.max_distance
        elif current_fps <= self.fps_threshold_low:
            return self.min_distance
        else:
            # Linear interpolation between min and max
            ratio = (current_fps - self.fps_threshold_low) / (self.fps_threshold_high - self.fps_threshold_low)
            return self.min_distance + (self.max_distance - self.min_distance) * ratio


class TargetTracker:
    """Tracks targets to prevent wobbling and ensure stable lock-on"""
    def __init__(self, stability_threshold=10):
        self.current_target = None
        self.target_history = []
        self.stability_threshold = stability_threshold
        self.lock_confidence = 0
        self.max_history = 5



class TargetTrackerMethods:
    """Methods for the TargetTracker class"""

    def update_target(self, detection, distance=None):
        """Update current target with stability tracking"""
        if detection is None:
            self.current_target = None
            self.lock_confidence = 0
            self.target_history.clear()
            return None

        target_center = (
            (detection['xmax'] + detection['xmin']) / 2,
            (detection['ymax'] + detection['ymin']) / 2
        )

        # If we have a current target, check if this is the same target
        if self.current_target is not None:
            current_center = self.current_target
            target_distance = math.sqrt(
                (target_center[0] - current_center[0])**2 +
                (target_center[1] - current_center[1])**2
            )

            # If target is close to current target, it's the same target
            if target_distance < self.stability_threshold:
                self.lock_confidence = min(self.lock_confidence + 1, 10)
                self.current_target = target_center
                return target_center
            else:
                # New target detected, reset tracking
                self.lock_confidence = 0
                self.current_target = target_center
                return target_center
        else:
            # First target detection
            self.current_target = target_center
            self.lock_confidence = 1
            return target_center

    def is_locked(self):
        """Check if we have a stable lock on target"""
        return self.lock_confidence >= 3


# Inherit the methods into TargetTracker
TargetTracker.update_target = TargetTrackerMethods.update_target
TargetTracker.is_locked = TargetTrackerMethods.is_locked

dynamic_aim = DynamicAimConfig()
target_tracker = TargetTracker(stability_threshold=15)  # Prevent target switching wobble

ui = threading.Thread(target=labels, args=())
ui.start()

# Monitor configuration
MONITOR_WIDTH = 1920#game res
MONITOR_HEIGHT = 1080#game res
MONITOR_SCALE = 5 #how much the screen shot is downsized by eg. 5 would be one fifth of the monitor dimensions
region = (int(MONITOR_WIDTH/2-MONITOR_WIDTH/MONITOR_SCALE/2),int(MONITOR_HEIGHT/2-MONITOR_HEIGHT/MONITOR_SCALE/2),int(MONITOR_WIDTH/2+MONITOR_WIDTH/MONITOR_SCALE/2),int(MONITOR_HEIGHT/2+MONITOR_HEIGHT/MONITOR_SCALE/2))
x,y,width,height = region
screenshot_center = [int((width-x)/2),int((height-y)/2)]

# State variables
aim_assist = False
silent_aim = False
aim_lock = False

# Delta-based cooldowns (much more efficient)
aim_assist_cooldown = DeltaCooldown(0.2)
silent_aim_cooldown = DeltaCooldown(0.2)
aim_lock_cooldown = DeltaCooldown(0.2)
aim_movement_cooldown = DeltaCooldown(0.01)
silent_movement_cooldown = DeltaCooldown(0.2)
aim_lock_movement_cooldown = DeltaCooldown(0.01)

# Initialize optimized detection engine
torch.cuda.set_device(0)
detection_engine = AsyncDetectionEngine(r"C:\Users\<USER>\Desktop\Screen Capture\custom_best.engine")
camera = bettercam.create(output_idx=0, output_color="RGB")

# FPS tracking
start_time = time.perf_counter()
frame_count = 0
current_fps = 60

# Optimized main loop with asynchronous detection
try:
    while True:
        loop_start = time.perf_counter()

        # Capture screenshot
        screenshot = camera.grab(region)
        if screenshot is None:
            continue

        # Submit frame for asynchronous detection (non-blocking)
        detection_engine.submit_frame(screenshot)

        # Get latest detection results (from previous frames)
        detections = detection_engine.get_latest_detections()

        # Find closest target efficiently (no pandas overhead)
        closest_part_distance = float('inf')
        closest_detection = None

        for detection in detections:
            center_x = (detection['xmax'] + detection['xmin']) / 2
            center_y = (detection['ymax'] + detection['ymin']) / 2

            distance = math.sqrt((center_x - screenshot_center[0])**2 + (center_y - screenshot_center[1])**2)

            if distance < closest_part_distance:
                closest_part_distance = distance
                closest_detection = detection

        # Update target tracker for stable lock-on
        stable_target = target_tracker.update_target(closest_detection, closest_part_distance)

        # Update FPS counter efficiently
        frame_count += 1
        current_time = time.perf_counter()
        if current_time - start_time >= 1.0:
            current_fps = frame_count / (current_time - start_time)
            fps_text = f"Fps: {int(current_fps)}"
            fps_label.config(text=fps_text)
            frame_count = 0
            start_time = current_time

        # Handle keyboard input with delta-based cooldowns
        if keyboard.is_pressed('f9') and aim_assist_cooldown.can_trigger():
            aim_assist = not aim_assist
            if aim_assist:
                assist_label.config(text="Aim Assist: Active", fg='green')
            else:
                assist_label.config(text="Aim Assist: Unactive", fg='red')
            print(f"Aim assist: {aim_assist}")

        if keyboard.is_pressed('f8') and silent_aim_cooldown.can_trigger():
            silent_aim = not silent_aim
            if silent_aim:
                silent_label.config(text="Silent Aim: Active", fg='green')
            else:
                silent_label.config(text="Silent Aim: Unactive", fg='red')
            print(f"Silent aim: {silent_aim}")

        # Handle F7 key for aim lock toggle (activate/deactivate feature)
        if keyboard.is_pressed('f7') and aim_lock_cooldown.can_trigger():
            aim_lock = not aim_lock
            if aim_lock:
                aim_lock_label.config(text="Aim Lock: Ready", fg='green')
            else:
                aim_lock_label.config(text="Aim Lock: Unactive", fg='red')
            print(f"Aim lock feature toggled: {aim_lock}")

        # Handle 0x05 mouse button for aim lock execution (when feature is active)
        mouse_button_0x05_pressed = win32api.GetAsyncKeyState(0x05) & 0x8000 > 0

        # Update UI to show current aim lock status
        if aim_lock and mouse_button_0x05_pressed:
            aim_lock_label.config(text="Aim Lock: Firing", fg='cyan')
        elif aim_lock and not mouse_button_0x05_pressed:
            aim_lock_label.config(text="Aim Lock: Ready", fg='green')
        elif not aim_lock:
            aim_lock_label.config(text="Aim Lock: Unactive", fg='red')


        # Optimized aiming logic with instant lock-on (no wobbling)
        if stable_target is not None and target_tracker.is_locked():
            # Use stable target coordinates to prevent wobbling
            target_center_x = stable_target[0]
            target_center_y = stable_target[1]

            # Calculate raw aim adjustments with proper coordinate system
            # Positive x_diff = move right, Negative x_diff = move left
            # Positive y_diff = move down, Negative y_diff = move up
            raw_x = target_center_x - screenshot_center[0]
            raw_y = target_center_y - screenshot_center[1]

            # AIM LOCK LOGIC - HIGHEST PRIORITY
            # F7 activates feature, then 0x05 button works like silent aim with auto-click
            # Aim lock works like silent aim but without returning to previous position
            if (aim_lock and mouse_button_0x05_pressed and
                aim_lock_movement_cooldown.can_trigger() and
                closest_part_distance <= activation_range):

                # Calculate head height for neck targeting (90% head, 10% neck)
                head_height = closest_detection['ymax'] - closest_detection['ymin']
                neck_offset = head_height * 0.1  # 10% of head height for neck targeting

                # Adjust Y coordinate to target 90% head + 10% neck (slightly lower)
                adjusted_target_y = target_center_y + neck_offset

                # Recalculate raw_y with the neck offset for aim lock
                raw_y_lock = adjusted_target_y - screenshot_center[1]

                # Use SILENT AIM calculation for direct movement (no smoothing)
                x_diff_lock = raw_x * AIM_SPEED_SILENT * target_multiply[MONITOR_SCALE]
                y_diff_lock = raw_y_lock * AIM_SPEED_SILENT * target_multiply[MONITOR_SCALE]

                data = f"aimlock{int(x_diff_lock)}:{int(y_diff_lock)}"
                try:
                    serialcomm.write(data.encode())
                except:
                    pass  # Handle serial communication errors gracefully

            # SEPARATE COORDINATE CALCULATION FOR SILENT AIM
            # Silent aim logic - uses original main.py calculation with no distance limit
            elif (silent_aim and
                silent_movement_cooldown.can_trigger() and
                win32api.GetAsyncKeyState(0x06) & 0x8000 > 0):

                # Check if target is within silent aim range (much larger than aim assist)
                if closest_part_distance <= SILENT_AIM_MAX_DISTANCE:
                    # Use original main.py calculation for silent aim
                    x_diff_silent = raw_x * AIM_SPEED_SILENT * target_multiply[MONITOR_SCALE]
                    y_diff_silent = raw_y * AIM_SPEED_SILENT * target_multiply[MONITOR_SCALE]

                    data = f"silent{int(x_diff_silent)}:{int(y_diff_silent)}"
                    try:
                        serialcomm.write(data.encode())
                    except:
                        pass  # Handle serial communication errors gracefully

            # SEPARATE COORDINATE CALCULATION FOR AIM ASSIST
            # Regular aim assist logic - INSTANT LOCK, NO WOBBLING
            elif (aim_assist and
                  aim_movement_cooldown.can_trigger() and
                  not ((win32api.GetAsyncKeyState(0x06) & 0x8000 > 0) or
                       (win32api.GetAsyncKeyState(0x11) & 0x8000 > 0))):

                # Get dynamic aim distance based on current FPS for aim assist only
                dynamic_aim_distance = dynamic_aim.get_aim_distance(current_fps)

                # Check if target is within aim assist range (smaller than silent aim)
                if closest_part_distance <= min(dynamic_aim_distance, AIM_ASSIST_MAX_DISTANCE):
                    # Calculate head height for neck targeting (90% head, 20% neck)
                    head_height = closest_detection['ymax'] - closest_detection['ymin']
                    neck_offset = head_height * 0.2  # 20% of head height for neck targeting

                    # Adjust Y coordinate to target 80% head + 20% neck (slightly lower)
                    adjusted_target_y = target_center_y + neck_offset

                    # Recalculate raw_y with the neck offset for aim assist only
                    raw_y_assist = adjusted_target_y - screenshot_center[1]

                    # Use optimized calculation for aim assist (prevents overshooting)
                    base_multiplier = 1.0  # Conservative multiplier for aim assist
                    x_diff_assist = raw_x * AIM_SPEED_ASSIST * base_multiplier
                    y_diff_assist = raw_y_assist * AIM_SPEED_ASSIST * base_multiplier

                    # INSTANT LOCK SYSTEM - Direct target acquisition
                    # No smoothing applied - immediate lock when target is detected
                    # This eliminates the left-right wobbling completely

                    # Only apply minimal adjustment for very tiny movements to prevent hardware jitter
                    if abs(x_diff_assist) < 2 and abs(y_diff_assist) < 2:
                        # Micro-movements - slight reduction to prevent hardware jitter
                        x_diff_assist *= 0.9
                        y_diff_assist *= 0.9
                    # For all other movements - INSTANT LOCK with full precision

                    data = f"{int(x_diff_assist)}:{int(y_diff_assist)}"
                    try:
                        serialcomm.write(data.encode())
                    except:
                        pass  # Handle serial communication errors gracefully

except KeyboardInterrupt:
    print("\nShutting down...")
    detection_engine.stop()
except Exception as e:
    print(f"Error: {e}")
    detection_engine.stop()
finally:
    detection_engine.stop()